/**
 * 测试财务导出功能
 * 验证项目汇总表的新格式：项目主行 + 周预算子行
 */

import { FinancialExportService } from './dist/services/financialExport.js';
import fs from 'fs';
import path from 'path';

async function testFinancialExport() {
  console.log('🚀 开始测试财务导出功能...');

  try {
    const exportService = new FinancialExportService();
    
    // 测试参数
    const params = {
      year: 2025,
      brandIds: [], // 空数组表示导出所有品牌
      includeCompleted: true,
      includeCancelled: false
    };

    console.log('📊 生成财务报表...', params);
    
    // 生成报表
    const buffer = await exportService.exportFinancialReport(params);
    
    // 保存到文件
    const outputPath = path.join(process.cwd(), 'test-output', `财务报表_${params.year}年_${new Date().getTime()}.xlsx`);
    
    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, buffer);
    
    console.log('✅ 财务报表生成成功！');
    console.log(`📁 文件保存位置: ${outputPath}`);
    console.log(`📏 文件大小: ${(buffer.length / 1024).toFixed(2)} KB`);
    
    // 验证文件是否存在
    if (fs.existsSync(outputPath)) {
      const stats = fs.statSync(outputPath);
      console.log(`📅 创建时间: ${stats.birthtime.toLocaleString('zh-CN')}`);
      console.log('🎉 测试完成！请打开Excel文件查看项目汇总表的新格式。');
    } else {
      console.error('❌ 文件保存失败');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  }
}

// 运行测试
testFinancialExport().catch(console.error);
